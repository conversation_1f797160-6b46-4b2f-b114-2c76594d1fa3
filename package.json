{"name": "aggregation-tool", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build --mode production", "preview": "vite preview", "lint:eslint": "eslint", "lint:fix": "eslint --fix", "lint:prettier": "prettier --write \"**/*.{js,cjs,ts,json,tsx,css,less,scss,vue,html,md}\"", "lint:lint-staged": "lint-staged", "prepare": "husky", "commit": "git add . && git-cz"}, "dependencies": {"@tailwindcss/vite": "^4.1.4", "@tanstack/vue-table": "^8.21.3", "@vee-validate/zod": "^4.15.0", "@vueuse/components": "^13.1.0", "@vueuse/core": "^13.1.0", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "imagekit": "^6.0.0", "live2d-motionsync": "^0.0.4", "lodash-es": "^4.17.21", "lucide-vue-next": "^0.503.0", "pinia": "^3.0.2", "pixi-live2d-display": "^0.4.0", "pixi.js": "6.4.2", "qs": "^6.14.0", "reka-ui": "^2.2.0", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.4", "tw-animate-css": "^1.2.8", "vconsole": "^3.15.1", "vee-validate": "^4.15.0", "vue": "^3.5.13", "vue-router": "^4.5.0", "vue-sonner": "^1.3.2", "zod": "^3.24.3"}, "devDependencies": {"@eslint/js": "^9.25.1", "@types/lodash-es": "^4.17.12", "@types/node": "^22.14.1", "@types/qs": "^6.9.18", "@vitejs/plugin-vue": "^5.2.2", "@vue/tsconfig": "^0.7.0", "commitizen": "^4.3.1", "cz-git": "^1.11.1", "eslint": "^9.25.1", "eslint-config-prettier": "^10.1.2", "eslint-plugin-vue": "^10.0.0", "globals": "^16.0.0", "husky": "^9.1.7", "lint-staged": "^15.5.1", "postcss": "^8.5.3", "postcss-html": "^1.8.0", "postcss-scss": "^4.0.9", "prettier": "^3.5.3", "typescript": "~5.7.2", "typescript-eslint": "^8.31.0", "vite": "^6.3.1", "vite-plugin-vconsole": "^2.1.1", "vite-plugin-vue-devtools": "^7.7.5", "vue-tsc": "^2.2.8"}, "config": {"commitizen": {"path": "./node_modules/cz-git"}}, "lint-staged": {"*.{js,ts}": ["eslint --fix", "prettier --write"], "*.{cjs,json}": ["prettier --write"], "*.{vue,html}": ["eslint --fix", "prettier --write"], "*.{scss,css}": ["prettier --write"], "*.md": ["prettier --write"]}, "packageManager": "pnpm@9.12.3+sha512.cce0f9de9c5a7c95bef944169cc5dfe8741abfb145078c0d508b868056848a87c81e626246cb60967cbd7fd29a6c062ef73ff840d96b3c86c40ac92cf4a813ee"}